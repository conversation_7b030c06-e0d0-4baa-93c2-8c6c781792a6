# help me create serializers for tag in the tag table only have name and description fields

# Solution:
from rest_framework import serializers
import app.documents.tag.models as tag_models


class TagSerializer:
    class Get(serializers.ModelSerializer):

        name = serializers.SerializerMethodField()

        def get_name(self, tag_instance):
            return str(tag_instance.name).replace("_", " ").title()

        class Meta:
            model = tag_models.Tag
            fields = ["id", "name", "color"]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = tag_models.Tag
            fields = [
                "name",
                "color",
            ]


class KnowledgeImageToTagBridgeSerializer:
    class Get(serializers.ModelSerializer):
        class Meta:
            model = tag_models.KnowledgeImageToTagBridge
            fields = ["id", "knowledge_image", "tag"]

    class Post(serializers.ModelSerializer):
        class Meta:
            model = tag_models.KnowledgeImageToTagBridge
            fields = [
                "knowledge_image",
                "tag",
            ]
