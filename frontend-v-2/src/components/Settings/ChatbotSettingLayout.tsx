import React, { cloneElement, useEffect, useState } from "react";
import { Box } from "@mui/material";
import Layout from "./Layout";
import useAuth from "@/hooks/useAuth";
import useAlert from "@/hooks/useAlert";
import { SettingObj, initialSettingObj } from "@/interfaces/setting";
import { updateSetting, useSetting } from "@/services/settings";
import { getEditedObject } from "@/utils/settings";
import { alertError, alertSuccess } from "@/utils/alert";
import LoadingIndicator from "../LoadingIndicator";
import { SaveSettingsSection } from "./SettingComponents";
import EmptyMessage from "../EmptyMessage";

const ChatbotSettingLayout = ({
  seoTitle,
  children,
  displaySaveButton = true,
}: {
  seoTitle: string;
  children: React.ReactElement;
  displaySaveButton?: boolean;
}) => {
  const { user } = useAuth();
  const { setAlertInfo } = useAlert();

  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [updatedSetting, setUpdatedSetting] =
    useState<SettingObj>(initialSettingObj);
  const [imageFile, setImageFile] = useState<File | null>(null);

  const {
    data: setting,
    isLoading: isSettingFetching,
    mutate: settingMutate,
  } = useSetting("external");

  useEffect(() => {
    if (setting) {
      setUpdatedSetting(setting);
    }
  }, [setting]);

  const isUserLoading =
    user === undefined || (user !== undefined && user.id === -1);

  const isSettingLoading = setting !== undefined && updatedSetting.id === -1;

  const isLoading = isUserLoading || isSettingFetching || isSettingLoading;

  const isSameSetting =
    setting !== undefined &&
    JSON.stringify(setting) === JSON.stringify(updatedSetting);

  const disabled =
    isSaving || isLoading || updatedSetting.tone.trim() === "" || isSameSetting;

  const handleUpdate = async () => {
    if (setting === undefined || disabled) return;

    setIsSaving(true);

    try {
      const { id, ...body } = getEditedObject(setting, updatedSetting);
      const isEmptySettingObj =
        Object.keys(body).length === 0 && body.constructor === Object;
      !isEmptySettingObj && (await updateSetting(id, body, imageFile));

      alertSuccess("Chatbot settings updated!", setAlertInfo);
      setImageFile(null);
      !isEmptySettingObj && settingMutate();
    } catch (error) {
      alertError("Failed to update chatbot settings.", setAlertInfo);
    }

    setIsSaving(false);
  };

  return (
    <Layout seoTitle={seoTitle}>
      {isLoading ? (
        <LoadingIndicator />
      ) : updatedSetting.id !== -1 ? (
        <>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 3,
              opacity: isSaving ? 0.5 : 1,
              pointerEvents: isSaving ? "none" : "auto",
            }}
          >
            {displaySaveButton
              ? cloneElement(children, {
                  setting: updatedSetting,
                  setSetting: setUpdatedSetting,
                  setImageFile,
                })
              : cloneElement(children, {
                  setting: updatedSetting,
                })}
          </Box>

          {displaySaveButton && (
            <SaveSettingsSection
              handleUpdate={handleUpdate}
              isSaving={isSaving}
              disabled={disabled}
            />
          )}
        </>
      ) : (
        <EmptyMessage title="No setting found" />
      )}
    </Layout>
  );
};

export default ChatbotSettingLayout;
