import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Badge,
  Typography,
  Divider,
  useTheme,
  useMediaQuery,
  IconButton,
} from "@mui/material";
import {
  ExpandLess,
  ExpandMore,
  ChevronLeft,
  ChevronRight,
} from "@mui/icons-material";

// Main navigation icons
import AssignmentOutlinedIcon from "@mui/icons-material/AssignmentOutlined";
import CenterFocusWeakOutlinedIcon from "@mui/icons-material/CenterFocusWeakOutlined";
import ChatOutlinedIcon from "@mui/icons-material/ChatOutlined";
import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
import PeopleAltOutlinedIcon from "@mui/icons-material/PeopleAltOutlined";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import DashboardOutlinedIcon from "@mui/icons-material/DashboardOutlined";
import GroupOutlinedIcon from "@mui/icons-material/GroupOutlined";
import AdminPanelSettingsOutlinedIcon from "@mui/icons-material/AdminPanelSettingsOutlined";
import PolicyOutlinedIcon from "@mui/icons-material/PolicyOutlined";

// Action sub-navigation icons
import EventNoteOutlinedIcon from "@mui/icons-material/EventNoteOutlined";
import SupportAgentOutlinedIcon from "@mui/icons-material/SupportAgentOutlined";
import PhoneMissedOutlinedIcon from "@mui/icons-material/PhoneMissedOutlined";
import ContactSupportOutlinedIcon from "@mui/icons-material/ContactSupportOutlined";
import ScheduleSendOutlinedIcon from "@mui/icons-material/ScheduleSendOutlined";
import EventBusyOutlinedIcon from "@mui/icons-material/EventBusyOutlined";
import BlockOutlinedIcon from "@mui/icons-material/BlockOutlined";

// Knowledge Base sub-navigation icons
import TopicOutlinedIcon from "@mui/icons-material/TopicOutlined";
import FolderSharedOutlinedIcon from "@mui/icons-material/FolderSharedOutlined";
import ImageOutlinedIcon from "@mui/icons-material/ImageOutlined";

// Settings sub-navigation icons
import GroupsOutlinedIcon from "@mui/icons-material/GroupsOutlined";
import SmartToyOutlinedIcon from "@mui/icons-material/SmartToyOutlined";
import IntegrationInstructionsOutlinedIcon from "@mui/icons-material/IntegrationInstructionsOutlined";
import SpeedIcon from "@mui/icons-material/Speed";
import BookmarkBorderOutlinedIcon from "@mui/icons-material/BookmarkBorderOutlined";

// Account sub-navigation icons
import AccountCircleOutlinedIcon from "@mui/icons-material/AccountCircleOutlined";

import useAuth from "@/hooks/useAuth";
import { getTenantSchemaName, getTenantUrl } from "@/utils/url";
import { getIntegrationUrlFromSource } from "@/utils/integration";
import { useActionCount, useAllCustomActions } from "@/services/action";
import { isUserTenantAdmin } from "@/utils/auth";

// Additional icons for custom actions
import FeedbackOutlinedIcon from "@mui/icons-material/FeedbackOutlined";

// Import material icons CSS for custom action icons
import "material-icons/iconfont/outlined.css";

const SIDEBAR_WIDTH = 280;
const SIDEBAR_COLLAPSED_WIDTH = 64;

interface SidebarProps {
  open: boolean;
  onToggle: () => void;
}

interface NavigationItem {
  label: string;
  url?: string;
  icon: React.ReactNode;
  isActive?: boolean;
  badge?: number;
  children?: NavigationItem[];
  disabled?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ open, onToggle }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const { pathname } = useRouter();
  const { user } = useAuth();
  const tenantSchemaName = getTenantSchemaName();
  const { data: actionData } = useActionCount();
  const { data: customActionData } = useAllCustomActions();

  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const handleExpandToggle = (itemLabel: string) => {
    setExpandedItems((prev) =>
      prev.includes(itemLabel)
        ? prev.filter((item) => item !== itemLabel)
        : [...prev, itemLabel]
    );
  };

  // Auto-expand sections based on current path
  React.useEffect(() => {
    const autoExpandItems: string[] = [];

    if (pathname.includes("/action")) {
      autoExpandItems.push("Actions");
    }
    if (pathname.includes("/setting")) {
      autoExpandItems.push("Settings");
    }
    if (pathname.includes("/account")) {
      autoExpandItems.push("Account");
    }
    if (
      pathname.includes("/knowledge-base") &&
      !pathname.includes("/setting/knowledge-base")
    ) {
      autoExpandItems.push("Knowledge Base");
    }

    setExpandedItems((prev) => {
      const newItems = [...new Set([...prev, ...autoExpandItems])];
      return newItems;
    });
  }, [pathname]);

  // Check if user is tenant admin
  const isTenantAdmin =
    user && isUserTenantAdmin(user.tenant_information, tenantSchemaName);

  // Check tenant-specific features
  const isAmelioOnly = tenantSchemaName === "amelio";
  const isNucleoOnly = tenantSchemaName === "nucleo";

  const getNavigationItems = (): NavigationItem[] => {
    if (tenantSchemaName === "") {
      // Global navigation for users without tenant
      return [
        {
          label: "My Sites",
          url: "/",
          icon: <DashboardOutlinedIcon />,
          isActive: pathname === "/",
        },
        {
          label: "Users",
          url: "/user",
          icon: <GroupOutlinedIcon />,
          isActive: pathname.includes("/user"),
        },
      ];
    }

    // Build custom actions
    const customActions: NavigationItem[] = customActionData?.results
      ? customActionData.results.map((cad) => ({
          label: cad.name,
          url: getTenantUrl(`/action/custom-action?tab=${cad.name}`),
          icon: cad.icon_svg ? (
            <Box component="span" className="material-icons-outlined">
              {cad.icon_svg}
            </Box>
          ) : (
            <FeedbackOutlinedIcon />
          ),
          isActive:
            pathname.includes(`/action/custom-action`) &&
            pathname.includes(cad.name),
          disabled: !cad.is_activated,
        }))
      : [];

    // Tenant-specific navigation
    const navigationItems: NavigationItem[] = [
      {
        label: "Actions",
        icon: <AssignmentOutlinedIcon />,
        isActive: pathname.includes("/action"),
        badge: actionData?.total || 0,
        children: [
          {
            label: "Appointment",
            url: getTenantUrl("/action/appointment"),
            icon: <EventNoteOutlinedIcon />,
            isActive: pathname.includes("/action/appointment"),
          },
          ...(isAmelioOnly
            ? [
                {
                  label: "Unavailable Slots",
                  url: getTenantUrl("/action/unavailable-slots"),
                  icon: <EventBusyOutlinedIcon />,
                  isActive: pathname.includes("/action/unavailable-slots"),
                },
                {
                  label: "Blacklist",
                  url: getTenantUrl("/action/blacklist"),
                  icon: <BlockOutlinedIcon />,
                  isActive: pathname.includes("/action/blacklist"),
                },
              ]
            : []),
          {
            label: "Customer Support",
            url: getTenantUrl("/action/customer-support"),
            icon: <SupportAgentOutlinedIcon />,
            isActive: pathname.includes("/action/customer-support"),
          },
          {
            label: "Callback Request",
            url: getTenantUrl("/action/callback-request"),
            icon: <PhoneMissedOutlinedIcon />,
            isActive: pathname.includes("/action/callback-request"),
          },
          {
            label: "Missing Information",
            url: getTenantUrl("/action/missing-information"),
            icon: <ContactSupportOutlinedIcon />,
            isActive: pathname.includes("/action/missing-information"),
          },
          {
            label: "Scheduled Message",
            url: getTenantUrl("/action/scheduled-message"),
            icon: <ScheduleSendOutlinedIcon />,
            isActive: pathname.includes("/action/scheduled-message"),
          },
          ...customActions,
        ],
      },
      {
        label: "Preview",
        url: getTenantUrl(
          `/preview/${getIntegrationUrlFromSource("Web Application")}`
        ),
        icon: <CenterFocusWeakOutlinedIcon />,
        isActive: pathname.includes("/preview"),
      },
      {
        label: "Chats",
        url: getTenantUrl("/chat"),
        icon: <ChatOutlinedIcon />,
        isActive: pathname.match(/\/chat(\/|$)/) !== null,
      },
      {
        label: "Knowledge Base",
        icon: <DescriptionOutlinedIcon />,
        isActive:
          pathname.includes("/knowledge-base") &&
          !pathname.includes("/setting/knowledge-base"),
        children: [
          {
            label: "Documents",
            url: getTenantUrl("/knowledge-base/document"),
            icon: <TopicOutlinedIcon />,
            isActive: pathname.includes("/knowledge-base/document"),
          },
          ...(isNucleoOnly
            ? [
                {
                  label: "Internal Documents",
                  url: getTenantUrl("/knowledge-base/internal-document"),
                  icon: <FolderSharedOutlinedIcon />,
                  isActive: pathname.includes(
                    "/knowledge-base/internal-document"
                  ),
                },
              ]
            : []),
          {
            label: "Images",
            url: getTenantUrl("/knowledge-base/image"),
            icon: <ImageOutlinedIcon />,
            isActive: pathname.includes("/knowledge-base/image"),
          },
        ],
      },
      {
        label: "CRM",
        url: getTenantUrl("/crm"),
        icon: <PeopleAltOutlinedIcon />,
        isActive: pathname.includes("/crm"),
      },
      {
        label: "Analytics",
        url: getTenantUrl("/analytics"),
        icon: <AssessmentOutlinedIcon />,
        isActive: pathname.includes("/analytics"),
      },
    ];

    return navigationItems;
  };

  const getSettingsItems = (): NavigationItem[] => {
    if (tenantSchemaName === "") return [];

    return [
      {
        label: "Settings",
        icon: <SettingsOutlinedIcon />,
        isActive: pathname.includes("/setting"),
        children: [
          {
            label: "Team",
            url: getTenantUrl("/setting/team"),
            icon: <GroupsOutlinedIcon />,
            isActive: pathname.includes("/setting/team"),
          },
          {
            label: "Chatbot",
            url: getTenantUrl("/setting/chatbot"),
            icon: <SmartToyOutlinedIcon />,
            isActive: pathname.includes("/setting/chatbot"),
          },
          {
            label: "Integrations",
            url: getTenantUrl("/setting/integration"),
            icon: <IntegrationInstructionsOutlinedIcon />,
            isActive: pathname.includes("/setting/integration"),
          },
          {
            label: "Knowledge Base",
            url: getTenantUrl("/setting/knowledge-base"),
            icon: <DescriptionOutlinedIcon />,
            isActive: pathname.includes("/setting/knowledge-base"),
          },
          {
            label: "Red Team Testing",
            url: getTenantUrl("/setting/red-team"),
            icon: <SpeedIcon />,
            isActive: pathname.includes("/setting/red-team"),
          },
          {
            label: "Subscription",
            url: getTenantUrl("/setting/subscription"),
            icon: <BookmarkBorderOutlinedIcon />,
            isActive: pathname.includes("/setting/subscription"),
          },
        ],
      },
    ];
  };

  const getAccountItems = (): NavigationItem[] => {
    if (tenantSchemaName === "") return [];

    const accountItems: NavigationItem[] = [
      {
        label: "Account",
        icon: <AccountCircleOutlinedIcon />,
        isActive: pathname.includes("/account"),
        children: [
          {
            label: "My Account",
            url: getTenantUrl("/account/detail"),
            icon: <AccountCircleOutlinedIcon />,
            isActive: pathname.includes("/account/detail"),
          },
        ],
      },
    ];

    return accountItems;
  };

  const getAdminItems = (): NavigationItem[] => {
    if (tenantSchemaName === "" || !user) return [];

    const adminItems: NavigationItem[] = [];

    // Admin panel for Admin role
    if (user.role === "Admin") {
      adminItems.push({
        label: "Admin",
        url: getTenantUrl("/admin"),
        icon: <AdminPanelSettingsOutlinedIcon />,
        isActive: pathname.includes("/admin"),
      });
    }

    // Policy generator for tenant admins
    if (isTenantAdmin) {
      adminItems.push({
        label: "Generate Policy",
        url: getTenantUrl("/policy-generator"),
        icon: <PolicyOutlinedIcon />,
        isActive: pathname.includes("/policy-generator"),
      });
    }

    return adminItems;
  };

  if (isMobile) {
    return (
      <Drawer
        variant="temporary"
        open={open}
        onClose={onToggle}
        sx={{
          "& .MuiDrawer-paper": {
            width: SIDEBAR_WIDTH,
            boxSizing: "border-box",
            borderRight: "1px solid",
            borderColor: "divider",
            backgroundColor: "background.default",
          },
        }}
      >
        <SidebarContent
          open={true}
          onToggle={onToggle}
          navigationItems={getNavigationItems()}
          settingsItems={getSettingsItems()}
          accountItems={getAccountItems()}
          adminItems={getAdminItems()}
          expandedItems={expandedItems}
          onExpandToggle={handleExpandToggle}
        />
      </Drawer>
    );
  }

  return (
    <Box
      sx={{
        width: open ? SIDEBAR_WIDTH : SIDEBAR_COLLAPSED_WIDTH,
        flexShrink: 0,
        borderRight: "1px solid",
        borderColor: "divider",
        backgroundColor: "background.default",
        transition: theme.transitions.create("width", {
          easing: theme.transitions.easing.sharp,
          duration: theme.transitions.duration.enteringScreen,
        }),
        overflowX: "hidden",
        height: "100%", // Full height of the container
      }}
    >
      <SidebarContent
        open={open}
        onToggle={onToggle}
        navigationItems={getNavigationItems()}
        settingsItems={getSettingsItems()}
        accountItems={getAccountItems()}
        adminItems={getAdminItems()}
        expandedItems={expandedItems}
        onExpandToggle={handleExpandToggle}
      />
    </Box>
  );
};

interface SidebarContentProps {
  open: boolean;
  onToggle: () => void;
  navigationItems: NavigationItem[];
  settingsItems: NavigationItem[];
  accountItems: NavigationItem[];
  adminItems: NavigationItem[];
  expandedItems: string[];
  onExpandToggle: (itemLabel: string) => void;
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  open,
  onToggle,
  navigationItems,
  settingsItems,
  accountItems,
  adminItems,
  expandedItems,
  onExpandToggle,
}) => {
  const theme = useTheme();

  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {
    const isExpanded = expandedItems.includes(item.label);
    const hasChildren = item.children && item.children.length > 0;

    return (
      <React.Fragment key={item.label}>
        <ListItem disablePadding sx={{ display: "block" }}>
          <ListItemButton
            component={item.url ? Link : "div"}
            href={item.url || ""}
            onClick={hasChildren ? () => onExpandToggle(item.label) : undefined}
            sx={{
              minHeight: 48,
              justifyContent: open ? "initial" : "center",
              px: 2.5,
              pl: level > 0 ? 4 + level * 2 : 2.5,
              backgroundColor: item.isActive
                ? "action.selected"
                : "transparent",
              "&:hover": {
                backgroundColor: "action.hover",
              },
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: 0,
                mr: open ? 3 : "auto",
                justifyContent: "center",
                color: item.isActive ? "primary.main" : "text.primary",
              }}
            >
              {item.badge && item.badge > 0 ? (
                <Badge
                  badgeContent={item.badge}
                  max={9}
                  color="primary"
                  sx={{ mr: "0 !important" }}
                >
                  {item.icon}
                </Badge>
              ) : (
                item.icon
              )}
            </ListItemIcon>
            <ListItemText
              primary={item.label}
              sx={{
                opacity: open ? 1 : 0,
                color: item.isActive ? "primary.main" : "text.primary",
                fontWeight: item.isActive ? "bold" : "normal",
              }}
            />
            {hasChildren &&
              open &&
              (isExpanded ? <ExpandLess /> : <ExpandMore />)}
          </ListItemButton>
        </ListItem>
        {hasChildren && (
          <Collapse in={isExpanded && open} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children?.map((child) =>
                renderNavigationItem(child, level + 1)
              )}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", height: "100%" }}>
      {/* Main navigation */}
      <Box sx={{ flexGrow: 1, overflowY: "auto" }}>
        <List>{navigationItems.map((item) => renderNavigationItem(item))}</List>

        {/* Settings section */}
        {settingsItems.length > 0 && (
          <>
            <Divider sx={{ my: 1 }} />
            <List>
              {settingsItems.map((item) => renderNavigationItem(item))}
            </List>
          </>
        )}

        {/* Account section */}
        {accountItems.length > 0 && (
          <>
            <Divider sx={{ my: 1 }} />
            <List>
              {accountItems.map((item) => renderNavigationItem(item))}
            </List>
          </>
        )}

        {/* Admin section */}
        {adminItems.length > 0 && (
          <>
            <Divider sx={{ my: 1 }} />
            <List>{adminItems.map((item) => renderNavigationItem(item))}</List>
          </>
        )}
      </Box>
    </Box>
  );
};

export default Sidebar;
