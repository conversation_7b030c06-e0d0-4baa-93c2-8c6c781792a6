import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { Box, useTheme, useMediaQuery } from "@mui/material";
import { useSubscription } from "@/contexts/subscription";
import useAuth from "@/hooks/useAuth";
import { clearLocalStorage } from "@/utils/auth";
import { getTenantSchemaName, getTenantUrl } from "@/utils/url";
import RootLayout from "./Layout";
import Header from "./Header";
import Sidebar from "./Sidebar";
import Redirecting from "../Redirecting";

const AppLayout = ({
  showHeader = true,
  useSidebar = true,
  children,
}: {
  showHeader?: boolean;
  useSidebar?: boolean;
  children: React.ReactNode;
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("lg"));
  const { replace } = useRouter();
  const { user } = useAuth();

  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);

  const isUserLoading = (user && user.id === -1) || user === undefined;

  const { isCurrentSubscriptionLoading, isSubscriptionExpired } =
    useSubscription();

  const isLoading = isUserLoading || isCurrentSubscriptionLoading;

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  useEffect(() => {
    if (user === undefined) {
      clearLocalStorage();
      replace(
        getTenantUrl(
          `/login?next=${encodeURIComponent(window.location.pathname)}`
        )
      );
    } else if (user && user.id !== -1) {
      const tenantSchemaName = getTenantSchemaName();

      if (tenantSchemaName === "") return;

      const userTenants = user.tenant_information.map((t) => t.schema_name);

      if (!userTenants.includes(tenantSchemaName)) {
        window.location.href = "/";
      }
    }
  }, [user, replace]);

  useEffect(() => {
    if (isLoading || !isSubscriptionExpired) return;

    replace(getTenantUrl("/subscription-update"));
  }, [isLoading, isSubscriptionExpired, replace]);

  // Update sidebar state based on screen size
  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  return (
    <RootLayout>
      {isLoading || isSubscriptionExpired ? (
        <Redirecting />
      ) : (
        <Box sx={{ display: "flex" }}>
          {useSidebar && (
            <Sidebar open={sidebarOpen} onToggle={handleSidebarToggle} />
          )}
          <Box
            component="main"
            sx={{
              flexGrow: 1,
              display: "flex",
              flexDirection: "column",
              minHeight: "100vh",
              ml: useSidebar && !isMobile ? 0 : 0,
            }}
          >
            {showHeader && (
              <Header
                showNavigation={!useSidebar}
                useSidebar={useSidebar}
                onSidebarToggle={handleSidebarToggle}
              />
            )}
            <Box
              sx={{
                flexGrow: 1,
                p: useSidebar ? 3 : 0,
                pt: showHeader && !useSidebar ? 0 : useSidebar ? 3 : 0,
              }}
            >
              {children}
            </Box>
          </Box>
        </Box>
      )}
    </RootLayout>
  );
};

export default AppLayout;
