import React from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import {
  AppBar,
  Box,
  Button,
  IconButton,
  Toolbar,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  useTheme,
  Badge,
} from "@mui/material";
import CenterFocusWeakOutlinedIcon from "@mui/icons-material/CenterFocusWeakOutlined";
import AssessmentOutlinedIcon from "@mui/icons-material/AssessmentOutlined";
import AssignmentOutlinedIcon from "@mui/icons-material/AssignmentOutlined";
import ChatOutlinedIcon from "@mui/icons-material/ChatOutlined";
import SettingsOutlinedIcon from "@mui/icons-material/SettingsOutlined";
import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
import PolicyOutlinedIcon from "@mui/icons-material/PolicyOutlined";
import LogoutIcon from "@mui/icons-material/Logout";
import MenuIcon from "@mui/icons-material/Menu";
import WbSunnyOutlinedIcon from "@mui/icons-material/WbSunnyOutlined";
import DarkModeOutlinedIcon from "@mui/icons-material/DarkModeOutlined";
import PeopleAltOutlinedIcon from "@mui/icons-material/PeopleAltOutlined";
import AccountCircleOutlinedIcon from "@mui/icons-material/AccountCircleOutlined";
import DashboardOutlinedIcon from "@mui/icons-material/DashboardOutlined";
import GroupOutlinedIcon from "@mui/icons-material/GroupOutlined";
import AdminPanelSettingsOutlinedIcon from "@mui/icons-material/AdminPanelSettingsOutlined";
import useAuth from "@/hooks/useAuth";
import useModal from "@/hooks/useModal";
import { clearLocalStorage, isUserTenantAdmin } from "@/utils/auth";
import { getTenantSchemaName, getTenantUrl } from "@/utils/url";
import { useColorMode } from "@/theme";
import MobileDrawer from "./MobileDrawer";
import Logo from "./Logo";
import { getIntegrationUrlFromSource } from "@/utils/integration";
import { useActionCount } from "@/services/action";
import CurrentSubscriptionInfo from "../Subscription/CurrentSubscriptionInfo";

const Header = ({
  showNavigation = true,
  useSidebar = false,
  onSidebarToggle,
}: {
  showNavigation?: boolean;
  useSidebar?: boolean;
  onSidebarToggle?: () => void;
}) => {
  const tenantSchemaName = getTenantSchemaName();
  const { user } = useAuth();
  const { pathname } = useRouter();
  const { open, handleOpen, handleClose } = useModal();

  const { data } = useActionCount();

  const links =
    tenantSchemaName !== ""
      ? [
          {
            label: "Actions",
            url: getTenantUrl("/action/appointment"),
            icon: <AssignmentOutlinedIcon fontSize="small" />,
            isActive: pathname.includes("/action"),
          },
          {
            label: "Preview",
            url: getTenantUrl(
              `/preview/${getIntegrationUrlFromSource("Web Application")}`
            ),
            icon: <CenterFocusWeakOutlinedIcon />,
            isActive: pathname.includes("/preview"),
          },
          {
            label: "Chats",
            url: getTenantUrl("/chat"),
            icon: <ChatOutlinedIcon />,
            isActive: pathname.match(/\/chat(\/|$)/) !== null,
          },
          {
            label: "Knowledge Base",
            url: getTenantUrl("/knowledge-base/document"),
            icon: <DescriptionOutlinedIcon />,
            isActive:
              pathname.includes("/knowledge-base") &&
              !pathname.includes("/setting/knowledge-base"),
          },
          {
            label: "CRM",
            url: getTenantUrl("/crm"),
            icon: <PeopleAltOutlinedIcon />,
            isActive: pathname.includes("/crm"),
          },
          {
            label: "Analytics",
            url: getTenantUrl("/analytics"),
            icon: <AssessmentOutlinedIcon />,
            isActive: pathname.includes("/analytics"),
          },
        ]
      : [
          {
            label: "My Sites",
            url: "/",
            icon: <DashboardOutlinedIcon />,
            isActive: pathname === "/",
            disabled: false,
          },
          {
            label: "Users",
            url: "/user",
            icon: <GroupOutlinedIcon />,
            isActive: pathname.includes("/user"),
            disabled: false,
          },
        ];

  return (
    <>
      {showNavigation && (
        <MobileDrawer open={open} handleClose={handleClose} links={links} />
      )}

      <AppBar
        position="fixed"
        elevation={0}
        sx={{
          width: "100%",
          backgroundColor: "background.default",
          color: "text.primary",
          borderBottom: "1px solid",
          borderColor: "divider",
          px: { xs: 2, lg: 12 },
        }}
      >
        <Toolbar
          disableGutters
          sx={{
            justifyContent: "space-between",
            gap: 1.5,
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {useSidebar && onSidebarToggle && (
              <IconButton
                onClick={onSidebarToggle}
                sx={{
                  color: "text.primary",
                  display: { xs: "flex", lg: "flex" },
                }}
              >
                <MenuIcon />
              </IconButton>
            )}
            <Logo haveOrgSwitcher={tenantSchemaName !== ""} />
          </Box>

          {showNavigation && (
            <Box
              sx={{
                display: {
                  xs: "flex",
                  lg: "none",
                },
              }}
            >
              <IconButton
                size="large"
                onClick={handleOpen}
                color="inherit"
                edge="end"
              >
                <MenuIcon />
              </IconButton>
            </Box>
          )}

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              flexGrow: 0,
            }}
          >
            {user && user.id !== -1 && (
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  gap: 3.5,
                }}
              >
                {showNavigation && !useSidebar && (
                  <Box
                    sx={{
                      display: {
                        xs: "none",
                        lg: "flex",
                      },
                      alignItems: "center",
                      gap: 3.5,
                    }}
                  >
                    {links.map((link, index) => (
                      <Button
                        key={index}
                        LinkComponent={Link}
                        href={link.url}
                        variant="text"
                        startIcon={
                          link.label === "Actions" ? (
                            <Badge
                              badgeContent={data?.total || 0}
                              max={9}
                              color="primary"
                              sx={{ mr: "0 !important" }}
                            >
                              {link.icon}
                            </Badge>
                          ) : (
                            link.icon
                          )
                        }
                        sx={{
                          textTransform: "none",
                          color: link.isActive
                            ? "primary.main"
                            : "text.primary",
                          fontWeight: "normal",
                          fontSize: 16,
                          p: 0,
                          pl: 0.5,
                          ":hover": {
                            color: "primary.main",
                            backgroundColor: "transparent",
                          },
                          ...(link.isActive && {
                            textShadow: (theme) =>
                              `0 0 1px ${theme.palette.primary.main}`,
                          }),
                        }}
                        disableRipple
                      >
                        {link.label}
                      </Button>
                    ))}
                  </Box>
                )}

                <AvatarIcon letter={user.email.substring(0, 1).toUpperCase()} />
              </Box>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      <Toolbar sx={{ minHeight: "81px !important" }} />
    </>
  );
};

export const AvatarIcon = ({ letter }: { letter: string }) => {
  const theme = useTheme();
  const colorMode = useColorMode();
  const tenantSchemaName = getTenantSchemaName();

  const { replace } = useRouter();
  const { user, setUser } = useAuth();

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleOpen = (event: React.MouseEvent<HTMLButtonElement>) =>
    setAnchorEl(event.currentTarget);
  const handleClose = () => setAnchorEl(null);

  const handleLogout = () => {
    clearLocalStorage();
    replace(getTenantUrl("/login"));
    handleClose();

    setTimeout(() => {
      setUser(undefined);
    }, 1000);
  };

  const isTenantAdmin =
    user && isUserTenantAdmin(user.tenant_information, tenantSchemaName);

  return (
    <>
      <IconButton
        size="small"
        onClick={handleOpen}
        disableRipple
        sx={{
          p: 0,
        }}
      >
        <Avatar
          sx={{
            color: "#fff",
            backgroundColor: "primary.main",
          }}
        >
          {letter}
        </Avatar>
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        transformOrigin={{ horizontal: "right", vertical: "top" }}
        anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
        sx={{
          ".MuiDivider-root": {
            my: 0.75,
          },
        }}
        PaperProps={{
          sx: {
            mt: 1,
            backgroundColor: "background.default",
          },
        }}
      >
        {user && tenantSchemaName && (
          <Link href={getTenantUrl("/setting/subscription")}>
            <MenuItem sx={{ color: "text.primary" }}>
              <CurrentSubscriptionInfo />
            </MenuItem>
          </Link>
        )}

        {user && tenantSchemaName && <Divider />}

        {user && user.role === "Admin" && tenantSchemaName && (
          <Link href={getTenantUrl("/admin")}>
            <MenuItem
              sx={{
                color: "text.primary",
              }}
            >
              <AdminPanelSettingsOutlinedIcon
                fontSize="small"
                sx={{
                  color: "text.primary",
                  mr: 1,
                }}
              />
              Admin
            </MenuItem>
          </Link>
        )}

        {isTenantAdmin && (
          <Link href={getTenantUrl("/policy-generator")}>
            <MenuItem
              sx={{
                color: "text.primary",
              }}
            >
              <PolicyOutlinedIcon
                fontSize="small"
                sx={{
                  color: "text.primary",
                  mr: 1,
                }}
              />
              Generate Policy
            </MenuItem>
          </Link>
        )}

        {isTenantAdmin && <Divider />}

        {user && tenantSchemaName && (
          <Link href={getTenantUrl("/account/detail")}>
            <MenuItem
              sx={{
                color: "text.primary",
              }}
            >
              <AccountCircleOutlinedIcon
                fontSize="small"
                sx={{
                  color: "text.primary",
                  mr: 1,
                }}
              />
              My Account
            </MenuItem>
          </Link>
        )}

        {user && tenantSchemaName && (
          <Link href={getTenantUrl("/setting/team")}>
            <MenuItem
              sx={{
                color: "text.primary",
              }}
            >
              <SettingsOutlinedIcon
                fontSize="small"
                sx={{
                  color: "text.primary",
                  mr: 1,
                }}
              />
              Settings
            </MenuItem>
          </Link>
        )}

        {user && tenantSchemaName && <Divider />}

        <MenuItem onClick={colorMode.toggleColorMode}>
          {theme.palette.mode === "dark" ? (
            <WbSunnyOutlinedIcon
              fontSize="small"
              sx={{
                color: "text.primary",
                mr: 1,
              }}
            />
          ) : (
            <DarkModeOutlinedIcon
              fontSize="small"
              sx={{
                color: "text.primary",
                mr: 1,
              }}
            />
          )}
          {`${theme.palette.mode === "dark" ? "Light" : "Dark"} Mode`}
        </MenuItem>

        <Divider />

        <MenuItem onClick={handleLogout}>
          <LogoutIcon
            fontSize="small"
            sx={{
              color: "text.primary",
              mr: 1,
            }}
          />
          Logout
        </MenuItem>
      </Menu>
    </>
  );
};

export default Header;
