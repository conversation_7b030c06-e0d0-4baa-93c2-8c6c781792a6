import React, { useEffect } from "react";
import { useRouter } from "next/router";
import Link from "next/link";
import { Box, Button, Typography } from "@mui/material";
import IntegrationInstructionsOutlinedIcon from "@mui/icons-material/IntegrationInstructionsOutlined";
import AppLayout from "../Layout/AppLayout";
import Seo from "../Seo";
import { getTenantUrl } from "@/utils/url";
import { TABS } from "../Integration/constants";

const Layout = ({
  seoTitle,
  children,
}: {
  seoTitle: string;
  children: React.ReactNode;
}) => {
  const { pathname, replace } = useRouter();

  const urlWithSlash = pathname.endsWith("/") ? pathname : `${pathname}/`;
  const PREVIEW_TABS = TABS.filter((tab) => tab.hasPreview);
  const tabIndex = PREVIEW_TABS.filter((tab) => tab.hasPreview).findIndex(
    (tab) => urlWithSlash.includes(`/${tab.link}/`)
  );

  useEffect(() => {
    if (tabIndex === -1) {
      replace(getTenantUrl(`/preview/${PREVIEW_TABS[0].link}`));
    }
  }, [tabIndex, replace, PREVIEW_TABS]);

  return (
    <AppLayout>
      <Seo templateTitle={seoTitle} />

      {tabIndex !== -1 && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            flex: 1,
            width: "100%",
            maxWidth: "1366px",
            mx: "auto",
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              flexWrap: "wrap",
              gap: 1,
              mb: 1,
            }}
          >
            <Typography
              component="h4"
              sx={{
                color: "text.primary",
                fontWeight: "bold",
                lineHeight: 1.25,
              }}
            >
              {PREVIEW_TABS[tabIndex].label} Preview
            </Typography>

            <Button
              component={Link}
              href={getTenantUrl(
                `/setting/integration/${PREVIEW_TABS[tabIndex].link}`
              )}
              variant="outlined"
              startIcon={<IntegrationInstructionsOutlinedIcon />}
              sx={{
                borderRadius: 2,
                textTransform: "none",
                py: 1,
                maxWidth: "400px",
                lineHeight: 1.25,
                backgroundColor: "background.default",
              }}
              suppressHydrationWarning
            >
              Integration
            </Button>
          </Box>

          {children}
        </Box>
      )}
    </AppLayout>
  );
};

export default Layout;
