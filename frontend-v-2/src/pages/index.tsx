import React, { useCallback, useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import { GridFilterModel, GridSortModel } from "@mui/x-data-grid";
import { Box, Typography } from "@mui/material";
import Seo from "@/components/Seo";
import RootLayout from "@/components/Layout";
import Redirecting from "@/components/Redirecting";
import AppLayout from "@/components/Layout/AppLayout";
import OrgTable from "@/components/Start/OrgTable";
import ConfirmationDialog from "@/dialogs/ConfirmationDialog";
import useAlert from "@/hooks/useAlert";
import useModal from "@/hooks/useModal";
import { Tenant } from "@/interfaces/tenant";
import { deleteTenant, useAllTenants } from "@/services/tenant";
import useAuth from "@/hooks/useAuth";
import { PaginationModel, QueryOptions } from "@/interfaces/pagination";
import AddSiteButton from "@/components/Start/AddSiteButton";

const Home = () => {
  const { replace } = useRouter();
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    setIsLoading(true);

    if (user === undefined) {
      replace("/login");
    } else if (user.id !== -1) {
      setIsLoading(false);
    }
  }, [user, replace]);

  return isLoading ? <LoadingState /> : <MySites />;
};

export default Home;

const LoadingState = () => (
  <RootLayout>
    <Seo />
    <Redirecting />
  </RootLayout>
);

const MySites = () => {
  const { setUser } = useAuth();

  const { setAlertInfo } = useAlert();
  const { open, handleOpen, handleClose } = useModal();

  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [selectedTenant, setSelectedTenant] = useState<Tenant | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  const [paginationModel, setPaginationModel] = useState<PaginationModel>({
    page: 0,
    pageSize: 25,
  });

  const [queryOptions, setQueryOptions] = useState<QueryOptions>({});

  const timeoutRef = useRef<number | null>(null);

  const handleFilterModelChange = useCallback(
    (filterModel: GridFilterModel) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = window.setTimeout(() => {
        setQueryOptions((prev) => {
          if (
            filterModel.quickFilterValues &&
            filterModel.quickFilterValues.length > 0
          ) {
            setPaginationModel((prev) => ({
              ...prev,
              page: 0,
            }));

            const search = filterModel.quickFilterValues.join(" ");
            return { ...prev, search };
          } else {
            const { search, ...others } = prev;
            return others;
          }
        });
      }, 300);
    },
    []
  );

  const handleSortModelChange = useCallback((sortModel: GridSortModel) => {
    setQueryOptions((prev) => {
      if (sortModel.length > 0) {
        setPaginationModel((prev) => ({
          ...prev,
          page: 0,
        }));

        const ordering = `${sortModel[0].sort === "desc" ? "-" : ""}${
          sortModel[0].field
        }`;
        return { ...prev, ordering };
      } else {
        const { ordering, ...others } = prev;
        return others;
      }
    });
  }, []);

  const {
    data: tenantsData,
    mutate,
    isLoading,
  } = useAllTenants({ ...paginationModel, ...queryOptions });

  const [rowCount, setRowCount] = useState<number>(
    tenantsData ? tenantsData.count : 0
  );

  useEffect(() => {
    setTenants(tenantsData ? tenantsData.results : []);

    setRowCount((prevRowCount) =>
      tenantsData ? tenantsData.count : prevRowCount
    );
  }, [tenantsData]);

  const handleOpenModal = async (tenant: Tenant) => {
    setSelectedTenant(tenant);
    handleOpen();
  };

  const handleCloseModal = () => {
    setTimeout(() => {
      setSelectedTenant(null);
    }, 1000);

    handleClose();
  };

  const handleRemove = async () => {
    if (selectedTenant === null) return;

    setIsDeleting(true);

    await deleteTenant({
      tenantSchemaName: selectedTenant.schema_name,
      onSuccess: () => {
        mutate();
        handleCloseModal();
      },
      setAlertInfo,
      setUser,
    });

    setIsDeleting(false);
  };

  return (
    <AppLayout>
      <Seo templateTitle="My Sites" />

      <Box
        className="image-bg"
        sx={{
          display: "flex",
          flexDirection: "column",
          flex: 1,
          px: { xs: 2, lg: 3 }, // Reduced padding for sidebar layout
          py: 3, // Reduced vertical padding
        }}
      >
        <Box
          sx={{
            display: "flex",
            alignItems: "flex-start",
            justifyContent: "space-between",
            flexWrap: "wrap",
            columnGap: 2,
            rowGap: 1,
            mb: 1.5,
          }}
        >
          <Box>
            <Typography
              component="h4"
              sx={{
                fontWeight: "bold",
                color: "text.primary",
                mb: 0.25,
              }}
            >
              My Sites
            </Typography>

            <Typography
              sx={{
                fontSize: 14,
              }}
            >
              Effortlessly view and manage your sites all in one place!
            </Typography>
          </Box>

          <AddSiteButton />
        </Box>

        <OrgTable
          tenants={tenants}
          handleOpenModal={handleOpenModal}
          rowCount={rowCount}
          paginationModel={paginationModel}
          setPaginationModel={setPaginationModel}
          handleFilterModelChange={handleFilterModelChange}
          handleSortModelChange={handleSortModelChange}
          isLoading={isLoading || isDeleting}
        />

        <ConfirmationDialog
          open={open}
          handleClose={handleCloseModal}
          title={`Confirm Remove ${selectedTenant?.name}?`}
          body="This action is irreversible."
          buttonText="Remove"
          buttonOnClick={() => handleRemove()}
          isButtonLoading={isDeleting}
        />
      </Box>
    </AppLayout>
  );
};
