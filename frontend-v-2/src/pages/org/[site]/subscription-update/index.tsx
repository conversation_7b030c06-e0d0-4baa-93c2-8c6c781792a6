import React from "react";
import { Box, Container, Typography } from "@mui/material";
import AppLayout from "@/components/Layout/AppLayout";
import Seo from "@/components/Seo";
import PricingSection from "@/components/Subscription/PricingSection";
import ManageSubscriptionButton from "@/components/Subscription/ManageSubscriptionButton";

const SubscriptionUpdate = () => (
  <AppLayout showHeader={false} useSidebar={false}>
    <Seo templateTitle="Subscription Update" />

    <Box
      className="image-bg"
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Container
        sx={{
          p: 3,
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            gap: 1,
            mb: 6,
          }}
        >
          <Typography
            variant="h1"
            sx={{
              fontSize: "2rem",
              fontWeight: "bold",
              textAlign: "center",
              color: "primary.main",
            }}
          >
            Continue Your Journey With Us!
          </Typography>

          <Typography
            sx={{
              textAlign: "center",
              color: "text.secondary",
              mb: 1,
            }}
          >
            Update your subscription now to maintain your access and enjoy all
            our features without interruption.
          </Typography>

          <ManageSubscriptionButton primary />
        </Box>

        <PricingSection />
      </Container>
    </Box>
  </AppLayout>
);

export default SubscriptionUpdate;
